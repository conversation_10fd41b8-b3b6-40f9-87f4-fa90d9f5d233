<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>反转链表可视化演示</title>
    <link rel="stylesheet" href="reverse-linked-list-styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🔗 反转链表可视化演示</h1>
            <p>通过动画演示迭代和递归两种方法反转链表的过程</p>
        </header>

        <!-- 输入控制区域 -->
        <div class="input-section">
            <div class="input-group">
                <label for="listInput">输入链表数据（用逗号分隔）：</label>
                <input type="text" id="listInput" placeholder="例如: 1,2,3,4,5" value="1,2,3,4,5">
                <button id="createListBtn">创建链表</button>
            </div>
            
            <div class="algorithm-selection">
                <label>选择算法：</label>
                <input type="radio" id="iterative" name="algorithm" value="iterative" checked>
                <label for="iterative">迭代方法</label>
                <input type="radio" id="recursive" name="algorithm" value="recursive">
                <label for="recursive">递归方法</label>
            </div>
        </div>

        <!-- 控制按钮区域 -->
        <div class="controls">
            <button id="startBtn">开始演示</button>
            <button id="pauseBtn" disabled>暂停</button>
            <button id="resumeBtn" disabled>继续</button>
            <button id="resetBtn">重置</button>
            <button id="stepBtn">单步执行</button>
        </div>

        <!-- 速度控制 -->
        <div class="speed-control">
            <label for="speedSlider">动画速度：</label>
            <input type="range" id="speedSlider" min="1" max="10" value="5">
            <span id="speedValue">5</span>
        </div>

        <!-- 可视化区域 -->
        <div class="visualization-area">
            <!-- 原始链表 -->
            <div class="list-container">
                <h3>原始链表</h3>
                <div id="originalList" class="linked-list"></div>
            </div>

            <!-- 反转过程 -->
            <div class="list-container">
                <h3>反转过程</h3>
                <div id="processList" class="linked-list"></div>
                
                <!-- 指针显示 -->
                <div class="pointers">
                    <div id="prevPointer" class="pointer prev-pointer">
                        <span>prev</span>
                        <div class="arrow"></div>
                    </div>
                    <div id="currentPointer" class="pointer current-pointer">
                        <span>current</span>
                        <div class="arrow"></div>
                    </div>
                    <div id="nextPointer" class="pointer next-pointer">
                        <span>next</span>
                        <div class="arrow"></div>
                    </div>
                </div>
            </div>

            <!-- 结果链表 -->
            <div class="list-container">
                <h3>反转结果</h3>
                <div id="resultList" class="linked-list"></div>
            </div>
        </div>

        <!-- 步骤说明区域 -->
        <div class="step-explanation">
            <h3>当前步骤说明</h3>
            <div id="stepDescription" class="step-text">
                点击"开始演示"开始可视化反转链表的过程
            </div>
            <div class="step-counter">
                步骤: <span id="stepCounter">0</span> / <span id="totalSteps">0</span>
            </div>
        </div>

        <!-- 算法复杂度信息 -->
        <div class="complexity-info">
            <h3>算法复杂度分析</h3>
            <div class="complexity-grid">
                <div class="complexity-item">
                    <h4>迭代方法</h4>
                    <p><strong>时间复杂度:</strong> O(n)</p>
                    <p><strong>空间复杂度:</strong> O(1)</p>
                    <p><strong>特点:</strong> 使用三个指针，逐个反转节点指向</p>
                </div>
                <div class="complexity-item">
                    <h4>递归方法</h4>
                    <p><strong>时间复杂度:</strong> O(n)</p>
                    <p><strong>空间复杂度:</strong> O(n)</p>
                    <p><strong>特点:</strong> 利用递归调用栈，从后往前反转</p>
                </div>
            </div>
        </div>

        <!-- 代码展示区域 -->
        <div class="code-section">
            <h3>算法代码</h3>
            <div class="code-tabs">
                <button class="tab-btn active" data-tab="iterative-code">迭代方法</button>
                <button class="tab-btn" data-tab="recursive-code">递归方法</button>
            </div>
            
            <div id="iterative-code" class="code-content active">
                <pre><code>// 迭代方法反转链表
function reverseIterative(head) {
    let prev = null;        // 前一个节点
    let current = head;     // 当前节点
    
    while (current !== null) {
        let next = current.next;    // 保存下一个节点
        current.next = prev;        // 反转指针
        prev = current;             // 移动prev
        current = next;             // 移动current
    }
    
    return prev;    // 返回新的头节点
}</code></pre>
            </div>
            
            <div id="recursive-code" class="code-content">
                <pre><code>// 递归方法反转链表
function reverseRecursive(head) {
    // 基础情况
    if (!head || !head.next) {
        return head;
    }
    
    // 递归反转剩余部分
    const newHead = reverseRecursive(head.next);
    
    // 反转当前连接
    head.next.next = head;
    head.next = null;
    
    return newHead;
}</code></pre>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="reverse-linked-list.js"></script>
    <script src="reverse-linked-list-animation.js"></script>
</body>
</html>
