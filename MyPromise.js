/**
 * 手写Promise实现
 * 完全遵循Promise/A+规范
 * 作者：AI助手
 * 创建时间：2025-08-04
 */

// Promise的三种状态常量
const PENDING = "pending"; // 等待状态
const FULFILLED = "fulfilled"; // 已完成状态
const REJECTED = "rejected"; // 已拒绝状态

/**
 * MyPromise类 - 手写Promise实现
 */
class MyPromise {
  /**
   * 构造函数
   * @param {Function} executor - 执行器函数，接收resolve和reject两个参数
   */
  constructor(executor) {
    // 初始化Promise状态为pending
    this.state = PENDING;

    // 存储成功的值
    this.value = undefined;

    // 存储失败的原因
    this.reason = undefined;

    // 存储成功回调函数队列
    this.onFulfilledCallbacks = [];

    // 存储失败回调函数队列
    this.onRejectedCallbacks = [];

    /**
     * resolve函数 - 将Promise状态改为fulfilled
     * @param {*} value - 成功的值
     */
    const resolve = (value) => {
      // 只有在pending状态下才能转换状态
      if (this.state === PENDING) {
        this.state = FULFILLED;
        this.value = value;

        // 异步执行所有成功回调
        this.onFulfilledCallbacks.forEach((callback) => {
          callback();
        });
      }
    };

    /**
     * reject函数 - 将Promise状态改为rejected
     * @param {*} reason - 失败的原因
     */
    const reject = (reason) => {
      // 只有在pending状态下才能转换状态
      if (this.state === PENDING) {
        this.state = REJECTED;
        this.reason = reason;

        // 异步执行所有失败回调
        this.onRejectedCallbacks.forEach((callback) => {
          callback();
        });
      }
    };

    // 立即执行executor函数，并捕获可能的异常
    try {
      executor(resolve, reject);
    } catch (error) {
      // 如果executor执行过程中抛出异常，直接reject
      reject(error);
    }
  }

  /**
   * then方法 - Promise的核心方法，支持链式调用
   * @param {Function} onFulfilled - 成功回调函数
   * @param {Function} onRejected - 失败回调函数
   * @returns {MyPromise} 返回新的Promise实例
   */
  then(onFulfilled, onRejected) {
    // 参数校验：如果不是函数，则使用默认函数
    onFulfilled =
      typeof onFulfilled === "function" ? onFulfilled : (value) => value;
    onRejected =
      typeof onRejected === "function"
        ? onRejected
        : (reason) => {
            throw reason;
          };

    // 返回新的Promise实例，支持链式调用
    const promise2 = new MyPromise((resolve, reject) => {
      /**
       * 处理fulfilled状态的逻辑
       */
      const handleFulfilled = () => {
        // 使用setTimeout模拟微任务，确保异步执行
        setTimeout(() => {
          try {
            // 执行成功回调函数
            const x = onFulfilled(this.value);
            // 处理返回值，决定promise2的状态
            this.resolvePromise(promise2, x, resolve, reject);
          } catch (error) {
            // 如果回调执行出错，reject promise2
            reject(error);
          }
        }, 0);
      };

      /**
       * 处理rejected状态的逻辑
       */
      const handleRejected = () => {
        // 使用setTimeout模拟微任务，确保异步执行
        setTimeout(() => {
          try {
            // 执行失败回调函数
            const x = onRejected(this.reason);
            // 处理返回值，决定promise2的状态
            this.resolvePromise(promise2, x, resolve, reject);
          } catch (error) {
            // 如果回调执行出错，reject promise2
            reject(error);
          }
        }, 0);
      };

      // 根据当前Promise的状态执行相应逻辑
      if (this.state === FULFILLED) {
        // 如果当前Promise已经fulfilled，直接执行成功回调
        handleFulfilled();
      } else if (this.state === REJECTED) {
        // 如果当前Promise已经rejected，直接执行失败回调
        handleRejected();
      } else if (this.state === PENDING) {
        // 如果当前Promise还在pending状态，将回调加入队列
        this.onFulfilledCallbacks.push(handleFulfilled);
        this.onRejectedCallbacks.push(handleRejected);
      }
    });

    return promise2;
  }

  /**
   * 处理Promise解析逻辑的核心方法
   * @param {MyPromise} promise2 - 新的Promise实例
   * @param {*} x - then回调的返回值
   * @param {Function} resolve - promise2的resolve函数
   * @param {Function} reject - promise2的reject函数
   */
  resolvePromise(promise2, x, resolve, reject) {
    // 防止循环引用
    if (promise2 === x) {
      reject(new TypeError("Chaining cycle detected for promise"));
      return;
    }

    // 防止多次调用
    let called = false;

    // 如果x是对象或函数，可能是thenable
    if ((typeof x === "object" && x !== null) || typeof x === "function") {
      try {
        // 获取x的then方法
        const then = x.then;

        if (typeof then === "function") {
          // 如果then是函数，说明x是thenable，调用then方法
          then.call(
            x,
            (y) => {
              if (called) return;
              called = true;
              // 递归处理，直到得到非thenable值
              this.resolvePromise(promise2, y, resolve, reject);
            },
            (r) => {
              if (called) return;
              called = true;
              reject(r);
            }
          );
        } else {
          // 如果then不是函数，直接resolve
          resolve(x);
        }
      } catch (error) {
        if (called) return;
        called = true;
        reject(error);
      }
    } else {
      // 如果x不是对象或函数，直接resolve
      resolve(x);
    }
  }

  /**
   * catch方法 - 捕获Promise的错误
   * @param {Function} onRejected - 错误处理函数
   * @returns {MyPromise} 返回新的Promise实例
   */
  catch(onRejected) {
    return this.then(null, onRejected);
  }

  /**
   * finally方法 - 无论Promise成功还是失败都会执行
   * @param {Function} onFinally - 最终执行的函数
   * @returns {MyPromise} 返回新的Promise实例
   */
  finally(onFinally) {
    return this.then(
      (value) => {
        return MyPromise.resolve(onFinally()).then(() => value);
      },
      (reason) => {
        return MyPromise.resolve(onFinally()).then(() => {
          throw reason;
        });
      }
    );
  }

  /**
   * Promise.resolve静态方法 - 创建一个已解决的Promise
   * @param {*} value - 要解决的值
   * @returns {MyPromise} 返回Promise实例
   */
  static resolve(value) {
    // 如果value已经是MyPromise实例，直接返回
    if (value instanceof MyPromise) {
      return value;
    }

    // 创建新的已解决的Promise
    return new MyPromise((resolve) => {
      resolve(value);
    });
  }

  /**
   * Promise.reject静态方法 - 创建一个已拒绝的Promise
   * @param {*} reason - 拒绝的原因
   * @returns {MyPromise} 返回Promise实例
   */
  static reject(reason) {
    return new MyPromise((resolve, reject) => {
      reject(reason);
    });
  }

  /**
   * Promise.all静态方法 - 等待所有Promise完成
   * @param {Array} promises - Promise数组
   * @returns {MyPromise} 返回Promise实例
   */
  static all(promises) {
    return new MyPromise((resolve, reject) => {
      // 参数校验
      if (!Array.isArray(promises)) {
        reject(new TypeError("Promise.all expects an array"));
        return;
      }

      const results = [];
      let completedCount = 0;
      const totalCount = promises.length;

      // 如果数组为空，直接resolve空数组
      if (totalCount === 0) {
        resolve(results);
        return;
      }

      promises.forEach((promise, index) => {
        // 将每个元素转换为Promise
        MyPromise.resolve(promise).then(
          (value) => {
            results[index] = value;
            completedCount++;

            // 所有Promise都完成时，resolve结果数组
            if (completedCount === totalCount) {
              resolve(results);
            }
          },
          (reason) => {
            // 任何一个Promise失败，立即reject
            reject(reason);
          }
        );
      });
    });
  }

  /**
   * Promise.race静态方法 - 返回第一个完成的Promise结果
   * @param {Array} promises - Promise数组
   * @returns {MyPromise} 返回Promise实例
   */
  static race(promises) {
    return new MyPromise((resolve, reject) => {
      // 参数校验
      if (!Array.isArray(promises)) {
        reject(new TypeError("Promise.race expects an array"));
        return;
      }

      promises.forEach((promise) => {
        // 将每个元素转换为Promise，第一个完成的决定结果
        MyPromise.resolve(promise).then(resolve, reject);
      });
    });
  }
}

// 导出MyPromise类
module.exports = MyPromise;

// 如果在浏览器环境中，将MyPromise挂载到全局对象
if (typeof window !== "undefined") {
  window.MyPromise = MyPromise;
}

// ==================== 测试用例 ====================

/**
 * 测试用例函数
 */
function runTests() {
  console.log("🚀 开始测试MyPromise实现...\n");

  // 测试1：基本的resolve和then
  console.log("📝 测试1：基本的resolve和then");
  const promise1 = new MyPromise((resolve, reject) => {
    setTimeout(() => {
      resolve("测试1成功");
    }, 100);
  });

  promise1.then((value) => {
    console.log("✅ 测试1结果:", value);
  });

  // 测试2：基本的reject和catch
  console.log("📝 测试2：基本的reject和catch");
  const promise2 = new MyPromise((resolve, reject) => {
    setTimeout(() => {
      reject("测试2失败");
    }, 200);
  });

  promise2.catch((reason) => {
    console.log("✅ 测试2结果:", reason);
  });

  // 测试3：链式调用
  console.log("📝 测试3：链式调用");
  const promise3 = new MyPromise((resolve) => {
    resolve(1);
  });

  promise3
    .then((value) => {
      console.log("✅ 测试3步骤1:", value);
      return value * 2;
    })
    .then((value) => {
      console.log("✅ 测试3步骤2:", value);
      return value * 3;
    })
    .then((value) => {
      console.log("✅ 测试3最终结果:", value);
    });

  // 测试4：Promise.resolve
  console.log("📝 测试4：Promise.resolve");
  MyPromise.resolve("静态resolve测试").then((value) => {
    console.log("✅ 测试4结果:", value);
  });

  // 测试5：Promise.reject
  console.log("📝 测试5：Promise.reject");
  MyPromise.reject("静态reject测试").catch((reason) => {
    console.log("✅ 测试5结果:", reason);
  });

  // 测试6：Promise.all
  console.log("📝 测试6：Promise.all");
  const promises = [
    MyPromise.resolve(1),
    MyPromise.resolve(2),
    new MyPromise((resolve) => {
      setTimeout(() => resolve(3), 300);
    }),
  ];

  MyPromise.all(promises).then((results) => {
    console.log("✅ 测试6结果:", results);
  });

  // 测试7：Promise.race
  console.log("📝 测试7：Promise.race");
  const racePromises = [
    new MyPromise((resolve) => {
      setTimeout(() => resolve("第一个完成"), 100);
    }),
    new MyPromise((resolve) => {
      setTimeout(() => resolve("第二个完成"), 200);
    }),
  ];

  MyPromise.race(racePromises).then((result) => {
    console.log("✅ 测试7结果:", result);
  });

  // 测试8：finally方法
  console.log("📝 测试8：finally方法");
  MyPromise.resolve("finally测试")
    .then((value) => {
      console.log("✅ 测试8 then:", value);
      return value;
    })
    .finally(() => {
      console.log("✅ 测试8 finally: 无论如何都会执行");
    })
    .then((value) => {
      console.log("✅ 测试8最终结果:", value);
    });

  // 测试9：错误处理
  console.log("📝 测试9：错误处理");
  new MyPromise((resolve, reject) => {
    throw new Error("构造函数中的错误");
  }).catch((error) => {
    console.log("✅ 测试9结果:", error.message);
  });

  // 测试10：循环引用检测
  console.log("📝 测试10：循环引用检测");
  const promise10 = new MyPromise((resolve) => {
    resolve(1);
  });

  const promise10_2 = promise10.then(() => {
    return promise10_2; // 循环引用
  });

  promise10_2.catch((error) => {
    console.log("✅ 测试10结果:", error.message);
  });

  console.log("\n🎉 所有测试已启动，请等待异步结果...");
}

// 运行测试（如果直接执行此文件）
if (require.main === module) {
  runTests();
}
