/**
 * 链表节点类
 * 用于构建单向链表的基本数据结构
 */
class ListNode {
    /**
     * 构造函数
     * @param {*} val - 节点存储的值
     * @param {ListNode|null} next - 指向下一个节点的指针，默认为null
     */
    constructor(val, next = null) {
        this.val = val;      // 节点值
        this.next = next;    // 指向下一个节点的指针
    }
}

/**
 * 链表工具类
 * 提供链表的基本操作和反转算法
 */
class LinkedList {
    /**
     * 构造函数
     */
    constructor() {
        this.head = null;    // 链表头指针
    }

    /**
     * 从数组创建链表
     * @param {Array} arr - 用于创建链表的数组
     * @returns {ListNode|null} 返回链表的头节点
     * 时间复杂度: O(n), 空间复杂度: O(n)
     */
    static fromArray(arr) {
        if (!arr || arr.length === 0) return null;
        
        const head = new ListNode(arr[0]);
        let current = head;
        
        // 遍历数组，逐个创建节点并连接
        for (let i = 1; i < arr.length; i++) {
            current.next = new ListNode(arr[i]);
            current = current.next;
        }
        
        return head;
    }

    /**
     * 将链表转换为数组
     * @param {ListNode|null} head - 链表头节点
     * @returns {Array} 包含链表所有值的数组
     * 时间复杂度: O(n), 空间复杂度: O(n)
     */
    static toArray(head) {
        const result = [];
        let current = head;
        
        // 遍历链表，将每个节点的值添加到数组中
        while (current) {
            result.push(current.val);
            current = current.next;
        }
        
        return result;
    }

    /**
     * 迭代方法反转链表
     * @param {ListNode|null} head - 原链表的头节点
     * @returns {ListNode|null} 反转后链表的头节点
     * 时间复杂度: O(n), 空间复杂度: O(1)
     * 
     * 算法思路：
     * 1. 使用三个指针：prev(前一个节点)、current(当前节点)、next(下一个节点)
     * 2. 遍历链表，逐个改变节点的指向
     * 3. 最终prev指向新的头节点
     */
    static reverseIterative(head) {
        let prev = null;        // 前一个节点，初始为null
        let current = head;     // 当前处理的节点
        
        // 遍历整个链表
        while (current !== null) {
            let next = current.next;    // 保存下一个节点，防止丢失
            current.next = prev;        // 反转当前节点的指针
            prev = current;             // 移动prev指针
            current = next;             // 移动current指针到下一个节点
        }
        
        return prev;    // prev现在指向新的头节点
    }

    /**
     * 递归方法反转链表
     * @param {ListNode|null} head - 原链表的头节点
     * @returns {ListNode|null} 反转后链表的头节点
     * 时间复杂度: O(n), 空间复杂度: O(n) - 由于递归调用栈
     * 
     * 算法思路：
     * 1. 递归到链表末尾
     * 2. 从后往前逐个反转节点指向
     * 3. 返回新的头节点
     */
    static reverseRecursive(head) {
        // 基础情况：空链表或只有一个节点
        if (!head || !head.next) {
            return head;
        }
        
        // 递归反转剩余部分，获得新的头节点
        const newHead = LinkedList.reverseRecursive(head.next);
        
        // 反转当前节点和下一个节点的连接
        head.next.next = head;  // 让下一个节点指向当前节点
        head.next = null;       // 断开当前节点指向下一个节点的连接
        
        return newHead;         // 返回新的头节点
    }

    /**
     * 打印链表（用于调试）
     * @param {ListNode|null} head - 链表头节点
     * @returns {string} 链表的字符串表示
     */
    static printList(head) {
        const values = LinkedList.toArray(head);
        return values.join(' -> ') + ' -> null';
    }
}

/**
 * 测试用例和示例
 */
function runTests() {
    console.log('=== 反转链表测试用例 ===\n');
    
    // 测试用例1: 正常链表
    console.log('测试用例1: [1, 2, 3, 4, 5]');
    let head1 = LinkedList.fromArray([1, 2, 3, 4, 5]);
    console.log('原链表:', LinkedList.printList(head1));
    
    let reversed1 = LinkedList.reverseIterative(head1);
    console.log('迭代反转:', LinkedList.printList(reversed1));
    
    // 重新创建原链表进行递归测试
    head1 = LinkedList.fromArray([1, 2, 3, 4, 5]);
    let recursiveReversed1 = LinkedList.reverseRecursive(head1);
    console.log('递归反转:', LinkedList.printList(recursiveReversed1));
    console.log();
    
    // 测试用例2: 单个节点
    console.log('测试用例2: [42]');
    let head2 = LinkedList.fromArray([42]);
    console.log('原链表:', LinkedList.printList(head2));
    
    let reversed2 = LinkedList.reverseIterative(head2);
    console.log('迭代反转:', LinkedList.printList(reversed2));
    console.log();
    
    // 测试用例3: 空链表
    console.log('测试用例3: []');
    let head3 = LinkedList.fromArray([]);
    console.log('原链表:', LinkedList.printList(head3));
    
    let reversed3 = LinkedList.reverseIterative(head3);
    console.log('迭代反转:', LinkedList.printList(reversed3));
    console.log();
    
    // 测试用例4: 两个节点
    console.log('测试用例4: [1, 2]');
    let head4 = LinkedList.fromArray([1, 2]);
    console.log('原链表:', LinkedList.printList(head4));
    
    let reversed4 = LinkedList.reverseIterative(head4);
    console.log('迭代反转:', LinkedList.printList(reversed4));
    console.log();
}

// 如果在Node.js环境中运行，执行测试
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ListNode, LinkedList };
    
    // 运行测试
    runTests();
} else {
    // 在浏览器环境中，将类暴露到全局作用域
    window.ListNode = ListNode;
    window.LinkedList = LinkedList;
}
