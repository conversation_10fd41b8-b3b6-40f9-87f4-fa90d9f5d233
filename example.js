/**
 * MyPromise使用示例
 * 展示如何使用我们手写的Promise类
 */

const MyPromise = require('./MyPromise');

console.log('🌟 MyPromise使用示例\n');

// 示例1：基本用法
console.log('📖 示例1：基本的异步操作');
function fetchUserData(userId) {
    return new MyPromise((resolve, reject) => {
        // 模拟异步API调用
        setTimeout(() => {
            if (userId > 0) {
                resolve({
                    id: userId,
                    name: `用户${userId}`,
                    email: `user${userId}@example.com`
                });
            } else {
                reject(new Error('无效的用户ID'));
            }
        }, 1000);
    });
}

fetchUserData(1)
    .then(user => {
        console.log('✅ 获取用户成功:', user);
        return user.id;
    })
    .then(userId => {
        console.log('✅ 用户ID:', userId);
    })
    .catch(error => {
        console.log('❌ 错误:', error.message);
    });

// 示例2：错误处理
console.log('\n📖 示例2：错误处理');
fetchUserData(-1)
    .then(user => {
        console.log('✅ 获取用户成功:', user);
    })
    .catch(error => {
        console.log('✅ 捕获到错误:', error.message);
    });

// 示例3：Promise.all的使用
console.log('\n📖 示例3：Promise.all - 并行执行多个异步操作');
const userIds = [1, 2, 3];
const userPromises = userIds.map(id => fetchUserData(id));

MyPromise.all(userPromises)
    .then(users => {
        console.log('✅ 所有用户数据:', users);
    })
    .catch(error => {
        console.log('❌ 获取用户失败:', error.message);
    });

// 示例4：Promise.race的使用
console.log('\n📖 示例4：Promise.race - 竞速执行');
const fastPromise = new MyPromise(resolve => {
    setTimeout(() => resolve('快速响应'), 500);
});

const slowPromise = new MyPromise(resolve => {
    setTimeout(() => resolve('慢速响应'), 1500);
});

MyPromise.race([fastPromise, slowPromise])
    .then(result => {
        console.log('✅ 最快的结果:', result);
    });

// 示例5：复杂的链式调用
console.log('\n📖 示例5：复杂的链式调用');
function processData(data) {
    return new MyPromise(resolve => {
        setTimeout(() => {
            resolve(data.toUpperCase());
        }, 300);
    });
}

function saveData(data) {
    return new MyPromise(resolve => {
        setTimeout(() => {
            resolve(`已保存: ${data}`);
        }, 200);
    });
}

MyPromise.resolve('hello world')
    .then(data => {
        console.log('✅ 原始数据:', data);
        return processData(data);
    })
    .then(processedData => {
        console.log('✅ 处理后数据:', processedData);
        return saveData(processedData);
    })
    .then(result => {
        console.log('✅ 最终结果:', result);
    })
    .finally(() => {
        console.log('✅ 数据处理流程完成');
    });

// 示例6：错误恢复
console.log('\n📖 示例6：错误恢复机制');
function unreliableOperation() {
    return new MyPromise((resolve, reject) => {
        setTimeout(() => {
            if (Math.random() > 0.5) {
                resolve('操作成功');
            } else {
                reject(new Error('操作失败'));
            }
        }, 800);
    });
}

unreliableOperation()
    .catch(error => {
        console.log('✅ 第一次尝试失败:', error.message);
        console.log('✅ 正在重试...');
        return unreliableOperation(); // 重试
    })
    .catch(error => {
        console.log('✅ 第二次尝试也失败:', error.message);
        return '使用默认值'; // 提供默认值
    })
    .then(result => {
        console.log('✅ 最终结果:', result);
    });

console.log('\n⏳ 等待所有异步操作完成...');
