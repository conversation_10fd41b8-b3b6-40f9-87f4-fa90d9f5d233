# MyPromise - 手写Promise实现

一个完全遵循Promise/A+规范的手写Promise实现，包含所有核心功能和高级特性。

## 🚀 功能特性

### 核心功能
- ✅ **三种状态管理**：pending、fulfilled、rejected
- ✅ **构造函数**：接收executor函数，支持resolve和reject
- ✅ **then方法**：支持链式调用，完整的Promise链处理
- ✅ **catch方法**：错误捕获和处理
- ✅ **finally方法**：无论成功失败都会执行的清理逻辑

### 高级功能
- ✅ **Promise.resolve**：创建已解决的Promise
- ✅ **Promise.reject**：创建已拒绝的Promise
- ✅ **Promise.all**：并行执行多个Promise，等待所有完成
- ✅ **Promise.race**：竞速执行，返回第一个完成的结果

### 规范遵循
- ✅ **Promise/A+规范**：完全遵循官方规范
- ✅ **异步执行**：使用setTimeout模拟微任务队列
- ✅ **循环引用检测**：防止Promise链中的循环引用
- ✅ **错误处理**：完善的异常捕获和传播机制
- ✅ **类型检查**：支持thenable对象的处理

## 📁 文件结构

```
├── MyPromise.js    # 主要的Promise实现文件
├── example.js      # 使用示例和演示
└── README.md       # 项目说明文档
```

## 🔧 使用方法

### 基本用法

```javascript
const MyPromise = require('./MyPromise');

// 创建Promise
const promise = new MyPromise((resolve, reject) => {
    setTimeout(() => {
        resolve('成功的结果');
    }, 1000);
});

// 使用then处理结果
promise.then(result => {
    console.log(result); // 输出: 成功的结果
});
```

### 链式调用

```javascript
MyPromise.resolve(1)
    .then(value => value * 2)
    .then(value => value * 3)
    .then(result => {
        console.log(result); // 输出: 6
    });
```

### 错误处理

```javascript
new MyPromise((resolve, reject) => {
    reject(new Error('出错了'));
})
.catch(error => {
    console.log(error.message); // 输出: 出错了
});
```

### 并行执行

```javascript
const promises = [
    MyPromise.resolve(1),
    MyPromise.resolve(2),
    MyPromise.resolve(3)
];

MyPromise.all(promises).then(results => {
    console.log(results); // 输出: [1, 2, 3]
});
```

### 竞速执行

```javascript
const promises = [
    new MyPromise(resolve => setTimeout(() => resolve('快'), 100)),
    new MyPromise(resolve => setTimeout(() => resolve('慢'), 200))
];

MyPromise.race(promises).then(result => {
    console.log(result); // 输出: 快
});
```

## 🧪 运行测试

```bash
# 运行内置测试用例
node MyPromise.js

# 运行使用示例
node example.js
```

## 📚 实现细节

### 状态管理
- `PENDING`：初始状态，可以转换为fulfilled或rejected
- `FULFILLED`：成功状态，不可再改变
- `REJECTED`：失败状态，不可再改变

### 异步处理
使用`setTimeout`模拟微任务队列，确保then回调在下一个事件循环中执行，符合Promise规范。

### 链式调用机制
每个`then`方法都返回一个新的Promise实例，通过`resolvePromise`方法处理返回值：
- 如果返回值是Promise，等待其解决
- 如果返回值是thenable对象，调用其then方法
- 如果返回值是普通值，直接resolve
- 如果抛出异常，reject新Promise

### 错误处理
- 构造函数中的异常会自动reject Promise
- then回调中的异常会reject返回的新Promise
- 支持错误在Promise链中的传播

## 🎯 测试覆盖

测试用例覆盖了以下场景：
1. 基本的resolve和then
2. 基本的reject和catch
3. 链式调用
4. 静态方法（resolve、reject、all、race）
5. finally方法
6. 错误处理
7. 循环引用检测
8. 异步执行时序

## 🔍 与原生Promise的对比

| 功能 | MyPromise | 原生Promise |
|------|-----------|-------------|
| 基本功能 | ✅ | ✅ |
| 链式调用 | ✅ | ✅ |
| 静态方法 | ✅ | ✅ |
| 错误处理 | ✅ | ✅ |
| 微任务队列 | 模拟实现 | 原生支持 |
| 性能 | 较低 | 高 |
| 兼容性 | 完全兼容 | 原生 |

## 📝 注意事项

1. **性能**：这是一个教学实现，性能不如原生Promise
2. **微任务**：使用setTimeout模拟，不是真正的微任务
3. **兼容性**：支持Node.js和现代浏览器环境
4. **用途**：主要用于学习Promise原理，不建议在生产环境使用

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个实现！

## 📄 许可证

MIT License
